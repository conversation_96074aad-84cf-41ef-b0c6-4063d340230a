{"name": "ai-tools", "version": "1.0.23", "description": "一个跨平台的Electron应用", "main": "src/main/main.js", "homepage": "https://github.com/laixiao/AiTools", "author": "xiaolai <<EMAIL>>", "license": "MIT", "scripts": {"start": "electron .", "dev": "electron . --dev", "prod": "electron . --production", "build": "node scripts/build-with-date.js", "build:win": "node scripts/build-with-date.js --win", "build:mac": "node scripts/build-with-date.js --mac", "build:linux": "node scripts/build-with-date.js --linux", "dist": "node scripts/build-with-date.js --publish=never", "publish": "node scripts/build-with-date.js --publish=always", "postinstall": "electron-builder install-app-deps", "setup": "node setup.js", "check": "node check-env.js", "create-icon": "node create-sample-icon.js", "patch": "node scripts/release-patch.js"}, "build": {"appId": "com.xiaolai.aitools", "productName": "AI重器", "artifactName": "${name}-${version}.${ext}", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "publisherName": "xia<PERSON>i"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Utility"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "publish": {"provider": "github", "owner": "la<PERSON><PERSON>o", "repo": "AiTools"}}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {"electron-updater": "^6.1.7"}, "keywords": []}